<script lang="ts">
	import { onMount, onD<PERSON>roy, createEventDispatcher } from 'svelte';
    import { LetterBoldOutline, LetterItalicOutline, LetterUnderlineOutline } from 'flowbite-svelte-icons';
    import { LinkOutline, ListOutline, OrderedListOutline, AlignLeftOutline } from 'flowbite-svelte-icons';
    import { AlignCenterOutline, AlignRightOutline, FontColorOutline } from 'flowbite-svelte-icons';
    import { UndoOutline, RedoOutline, SubscriptOutline, SuperscriptOutline, CodeOutline } from 'flowbite-svelte-icons';
    import ColorSelectionModal from './RichTextAreaColorSelectionModal.svelte';

	const dispatch = createEventDispatcher();

	// @ts-ignore
	import { Editor } from '@tiptap/core';
	// @ts-ignore
	import { StarterKit } from '@tiptap/starter-kit';
	// @ts-ignore
	import { TextAlign } from '@tiptap/extension-text-align';
	// @ts-ignore
	import { TextStyle } from '@tiptap/extension-text-style';
	// @ts-ignore
	// import { Color } from '@tiptap/extension-color';
    import { Color } from '@tiptap/extension-text-style'
    // @ts-ignore
    import { Bold } from '@tiptap/extension-bold'
    // @ts-ignore
    import { Underline } from '@tiptap/extension-underline'
    // @ts-ignore
    import { Italic } from '@tiptap/extension-italic'
    // @ts-ignore
    import { Link } from '@tiptap/extension-link'
    // @ts-ignore
    import { Strike } from '@tiptap/extension-strike'
    // @ts-ignore
    import { Subscript } from '@tiptap/extension-subscript'
    // @ts-ignore
    import { Superscript } from '@tiptap/extension-superscript'
    // @ts-ignore
    import { Code } from '@tiptap/extension-code'

	export let content: string = '';

	let element: HTMLElement;
	let editor: any;
	let isUpdatingFromParent = false;
	let lastExternalContent = '';

	// Reactive statement to handle content changes from parent
	// Only update if the content actually changed from external source
	$: if (editor && content !== lastExternalContent && !isUpdatingFromParent && editor.isFocused === false) {
		// Only update when editor is not focused to avoid disrupting user interaction
		lastExternalContent = content;
		editor.commands.setContent(content, false);
	}

	onMount(() => {
		editor = new Editor({
			element: element,
			extensions: [
				StarterKit,
				TextAlign.configure({
					types: ['heading', 'paragraph'],
				}),
				TextStyle,
				Color,
				Bold,
				Underline,
				Italic,
				Link.configure({
					openOnClick: false,
				}),
				Strike,
				Subscript,
				Superscript,
				Code
			],
			content: content,
			editorProps: {
				attributes: {
					class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4',
				},
			},
			onTransaction: () => {
				// force re-render so `editor.isActive` works as expected
				editor = editor;
			},
			onUpdate: ({ editor }) => {
				// Prevent infinite loop when updating from parent
				isUpdatingFromParent = true;

				// Dispatch update event with editor instance
				dispatch('update', { editor });
				// Update content prop and track it as the last external content
				const newContent = editor.getHTML();
				content = newContent;
				lastExternalContent = newContent;

				// Reset flag after a tick
				setTimeout(() => {
					isUpdatingFromParent = false;
				}, 0);
			},
		});

		// Initialize lastExternalContent with the initial content
		lastExternalContent = content;
	});

	onDestroy(() => {
		if (editor) {
			editor.destroy();
		}
	});

	// Helper function to toggle link
	const toggleLink = () => {
		const previousUrl = editor.getAttributes('link').href;
		const url = window.prompt('URL', previousUrl);

		// cancelled
		if (url === null) {
			return;
		}

		// empty
		if (url === '') {
			editor.chain().focus().extendMarkRange('link').unsetLink().run();
			return;
		}

		// update link
		editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
	};

    let selectedColor = 'black';
    let isColorModalOpen = false;

    // Function to open color selection modal
    const openColorModal = () => {
        isColorModalOpen = true;
    };

    // Function to handle color selection from modal
    const handleColorSelected = (event: CustomEvent<{ color: string }>) => {
        const newColor = event.detail.color;
        selectedColor = newColor;
        editor.chain().focus().setColor(newColor).run();
        isColorModalOpen = false;
    };

    // Function to close color modal
    const handleColorModalClose = () => {
        isColorModalOpen = false;
    };
</script>

<!-- Editor Container -->
<div class="tiptap-editor-container">
	<!-- Editor Content Area -->
	<div class="editor-content-area">
		<div bind:this={element} class="editor-content" />
	</div>

	<!-- Bottom Toolbar -->
	{#if editor}
		<div class="toolbar">
			<!-- Undo/Redo -->
			<button
				class="toolbar-btn"
				on:click={() => editor.chain().focus().undo().run()}
				disabled={!editor.can().undo()}
				title="Undo"
			>
				<UndoOutline />
			</button>
			<button
				class="toolbar-btn"
				on:click={() => editor.chain().focus().redo().run()}
				disabled={!editor.can().redo()}
				title="Redo"
			>
				<RedoOutline />
			</button>

			<!-- Separator -->
			<div class="toolbar-separator"></div>

			<!-- Font Family Dropdown -->
			<!-- <div class="toolbar-dropdown">
				<select class="font-select">
					<option>Sans Serif</option>
					<option>Serif</option>
					<option>Monospace</option>
				</select>
				<svg class="w-3 h-3 dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
				</svg>
			</div> -->

			<!-- Text Size -->
			<!-- <div class="toolbar-dropdown">
				<select class="size-select">
					<option>TT</option>
				</select>
				<svg class="w-3 h-3 dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
				</svg>
			</div> -->

			<!-- Separator -->
			<!-- <div class="toolbar-separator"></div> -->

			<!-- Bold -->
			<button
				class="toolbar-btn"
				class:active={editor.isActive('bold')}
				on:click={() => editor.chain().focus().toggleBold().run()}
				title="Bold"
			>
				<LetterBoldOutline />
			</button>

			<!-- Italic -->
			<button
				class="toolbar-btn"
				class:active={editor.isActive('italic')}
				on:click={() => editor.chain().focus().toggleItalic().run()}
				title="Italic"
			>
				<LetterItalicOutline />
			</button>

			<!-- Underline -->
			<button
				class="toolbar-btn"
				class:active={editor.isActive('underline')}
				on:click={() => editor.chain().focus().toggleUnderline().run()}
				title="Underline"
			>
				<LetterUnderlineOutline />
			</button>

            <!-- Subscript -->
            <button
				class="toolbar-btn"
				class:active={editor.isActive('subscript')}
				on:click={() => editor.chain().focus().toggleSubscript().run()}
				title="Subscript"
			>
				<SubscriptOutline />
			</button>

            <!-- Superscript -->
            <button
				class="toolbar-btn"
				class:active={editor.isActive('superscript')}
				on:click={() => editor.chain().focus().toggleSuperscript().run()}
				title="Superscript"
			>
				<SuperscriptOutline />
			</button>

			<!-- Text Color -->
            <button
                class="toolbar-btn"
                on:click={openColorModal}
                title="Text Color"
            >
                <FontColorOutline />
            </button>

			<!-- Separator -->
			<div class="toolbar-separator"></div>

			<!-- Align Left -->
			<button
				class="toolbar-btn"
				class:active={editor.isActive({ textAlign: 'left' })}
				on:click={() => editor.chain().focus().setTextAlign('left').run()}
				title="Align Left"
			>
				<AlignLeftOutline />
			</button>

            <!-- Align Center -->
            <button
				class="toolbar-btn"
				class:active={editor.isActive({ textAlign: 'center' })}
				on:click={() => editor.chain().focus().setTextAlign('center').run()}
				title="Align Center"
			>
                <AlignCenterOutline />
			</button>

            <!-- Align Right -->
            <button
				class="toolbar-btn"
				class:active={editor.isActive({ textAlign: 'right' })}
				on:click={() => editor.chain().focus().setTextAlign('right').run()}
				title="Align Right"
			>
                <AlignRightOutline />
			</button>

            <!-- Separator -->
			<div class="toolbar-separator"></div>

			<!-- Bullet List -->
			<button
				class="toolbar-btn"
				class:active={editor.isActive('bulletList')}
				on:click={() => editor.chain().focus().toggleBulletList().run()}
				title="Bullet List"
			>
                <ListOutline />
			</button>

			<!-- Numbered List -->
			<button
				class="toolbar-btn"
				class:active={editor.isActive('orderedList')}
				on:click={() => editor.chain().focus().toggleOrderedList().run()}
				title="Numbered List"
			>
				<OrderedListOutline />
			</button>

            <!-- Separator -->
			<div class="toolbar-separator"></div>

			<!-- Link -->
			<button
				class="toolbar-btn"
				class:active={editor.isActive('link')}
				on:click={toggleLink}
				title="Link"
			>
				<LinkOutline />
			</button>

            <!-- Code -->
            <button
				class="toolbar-btn"
				class:active={editor.isActive('code')}
				on:click={() => editor.chain().focus().toggleCode().run()}
				title="Code"
			>
				<CodeOutline />
			</button>

			<!-- Indent -->
			<!-- <button class="toolbar-btn" title="Increase Indent">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 1v4m0 0h-4m4 0l-5-5"/>
				</svg>
			</button> -->

			<!-- Outdent -->
			<!-- <button class="toolbar-btn" title="Decrease Indent">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 1v4m0 0h-4m4 0l-5-5"/>
				</svg>
			</button> -->

			<!-- More Options -->
			<!-- <button class="toolbar-btn" title="More Options">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
				</svg>
			</button> -->
		</div>
	{/if}
</div>

<!-- Color Selection Modal -->
<ColorSelectionModal
    bind:isOpen={isColorModalOpen}
    {selectedColor}
    on:colorSelected={handleColorSelected}
    on:close={handleColorModalClose}
/>

<style>
	.tiptap-editor-container {
		background-color: white;
		display: flex;
		flex-direction: column;
		position: relative;
	}

	.editor-content-area {
		/* height: 300px; */
		overflow-y: auto;
		background-color: white;
		border-radius: 8px 8px 0 0;
	}

	.editor-content {
		width: 100%;
		height: 100%;
	}

	/* Tiptap editor content styling */
	:global(.tiptap-editor-container .ProseMirror) {
		outline: none;
		border: none;
		padding: 16px;
		min-height: 100px;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		font-size: 14px;
		line-height: 1.5;
		color: #374151;
	}

	:global(.tiptap-editor-container .ProseMirror p) {
		margin-bottom: 12px;
	}

	:global(.tiptap-editor-container .ProseMirror h1) {
		font-size: 1.5rem;
		font-weight: bold;
		margin-bottom: 16px;
	}

	:global(.tiptap-editor-container .ProseMirror h2) {
		font-size: 1.25rem;
		font-weight: bold;
		margin-bottom: 12px;
	}

	:global(.tiptap-editor-container .ProseMirror ul) {
		list-style-type: disc;
		padding-left: 24px;
		margin-bottom: 12px;
	}

	:global(.tiptap-editor-container .ProseMirror ol) {
		list-style-type: decimal;
		padding-left: 24px;
		margin-bottom: 12px;
	}

	:global(.tiptap-editor-container .ProseMirror li) {
		margin-bottom: 4px;
	}

	:global(.tiptap-editor-container .ProseMirror strong) {
		font-weight: bold;
	}

	:global(.tiptap-editor-container .ProseMirror em) {
		font-style: italic;
	}

	:global(.tiptap-editor-container .ProseMirror u) {
		text-decoration: underline;
	}

	:global(.tiptap-editor-container .ProseMirror a) {
		color: #2563eb;
		text-decoration: underline;
	}

	/* Toolbar styling */
	.toolbar {
		display: flex;
		align-items: center;
		gap: 4px;
		padding: 8px 12px;
		background-color: #f9fafb;
		border-top: 1px solid #e5e7eb;
		border-radius: 0 0 8px 8px;
		min-height: 44px;
		position: sticky;
		bottom: 0;
		z-index: 10;
	}

	.toolbar-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 32px;
		height: 32px;
		border-radius: 4px;
		border: none;
		background: transparent;
		color: #374151;
		font-size: 14px;
		cursor: pointer;
		transition: background-color 0.15s ease-in-out;
	}

	.toolbar-btn:hover {
		background-color: #e5e7eb;
	}

	.toolbar-btn:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	.toolbar-btn.active {
		background-color: #e5e7eb;
		color: #111827;
	}

	.toolbar-separator {
		width: 1px;
		height: 24px;
		background-color: #d1d5db;
		margin: 0 4px;
	}



	/* Responsive adjustments */
	@media (max-width: 640px) {
		.toolbar {
			flex-wrap: wrap;
			gap: 4px;
			padding: 8px;
		}

		.toolbar-btn {
			width: 28px;
			height: 28px;
		}
	}
</style>